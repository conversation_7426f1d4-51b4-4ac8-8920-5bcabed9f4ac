import 'dart:async';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:get/get.dart';

class ServerTimeService extends GetxController {
  static ServerTimeService get instance => Get.find();

  // Server time offset from device time (in milliseconds)
  int _timeOffset = 0;
  
  // Last sync timestamp
  DateTime? _lastSyncTime;
  
  // Sync interval (5 minutes)
  static const Duration _syncInterval = Duration(minutes: 5);
  
  // Timer for periodic sync
  Timer? _syncTimer;
  
  // Loading state
  final RxBool isLoading = false.obs;
  
  // Error state
  final RxString error = ''.obs;

  @override
  void onInit() {
    super.onInit();
    _initializeServerTime();
  }

  @override
  void onClose() {
    _syncTimer?.cancel();
    super.onClose();
  }

  /// Initialize server time synchronization
  Future<void> _initializeServerTime() async {
    await syncWithServer();
    _startPeriodicSync();
  }

  /// Start periodic synchronization with server
  void _startPeriodicSync() {
    _syncTimer?.cancel();
    _syncTimer = Timer.periodic(_syncInterval, (timer) {
      syncWithServer();
    });
  }

  /// Sync time with server
  Future<bool> syncWithServer() async {
    try {
      isLoading.value = true;
      error.value = '';

      final response = await http.get(
        Uri.parse('https://zero-koin-backend.vercel.app/api/users/server-time'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final serverTimestamp = data['timestamp'] as int;
        final deviceTimestamp = DateTime.now().millisecondsSinceEpoch;
        
        // Calculate offset (server time - device time)
        _timeOffset = serverTimestamp - deviceTimestamp;
        _lastSyncTime = DateTime.now();
        
        print('Server time synced. Offset: ${_timeOffset}ms');
        return true;
      } else {
        error.value = 'Failed to sync with server: ${response.statusCode}';
        print('Server time sync failed: ${response.statusCode} - ${response.body}');
        return false;
      }
    } catch (e) {
      error.value = 'Network error during time sync: $e';
      print('Server time sync error: $e');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// Get current server time
  DateTime getServerTime() {
    final deviceTime = DateTime.now();
    final serverTime = DateTime.fromMillisecondsSinceEpoch(
      deviceTime.millisecondsSinceEpoch + _timeOffset,
    );
    return serverTime;
  }

  /// Check if time sync is recent (within last 10 minutes)
  bool get isTimeSyncRecent {
    if (_lastSyncTime == null) return false;
    final timeSinceSync = DateTime.now().difference(_lastSyncTime!);
    return timeSinceSync.inMinutes < 10;
  }

  /// Force sync with server (useful for critical operations)
  Future<bool> forceSyncWithServer() async {
    print('Force syncing with server...');
    return await syncWithServer();
  }

  /// Get time offset in milliseconds
  int get timeOffset => _timeOffset;

  /// Get last sync time
  DateTime? get lastSyncTime => _lastSyncTime;

  /// Calculate remaining time until a future server timestamp
  Duration getRemainingTime(DateTime targetServerTime) {
    final currentServerTime = getServerTime();
    final remaining = targetServerTime.difference(currentServerTime);
    return remaining.isNegative ? Duration.zero : remaining;
  }

  /// Calculate remaining seconds until a future server timestamp
  int getRemainingSeconds(DateTime targetServerTime) {
    final remaining = getRemainingTime(targetServerTime);
    return remaining.inSeconds;
  }

  /// Validate if a session should be unlocked based on server time
  bool isSessionUnlocked(DateTime? nextUnlockAt) {
    if (nextUnlockAt == null) return true;
    final serverTime = getServerTime();
    return serverTime.isAfter(nextUnlockAt) || serverTime.isAtSameMomentAs(nextUnlockAt);
  }

  /// Get formatted countdown string for remaining time
  String getFormattedCountdown(DateTime targetServerTime) {
    final remaining = getRemainingSeconds(targetServerTime);
    if (remaining <= 0) return '00:00:00';
    
    final hours = remaining ~/ 3600;
    final minutes = (remaining % 3600) ~/ 60;
    final seconds = remaining % 60;
    
    return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }
}
