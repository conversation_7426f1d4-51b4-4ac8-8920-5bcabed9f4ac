import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:zero_koin/services/server_time_service.dart';
import 'package:zero_koin/controllers/session_controller.dart';

class TimeTestScreen extends StatefulWidget {
  const TimeTestScreen({super.key});

  @override
  State<TimeTestScreen> createState() => _TimeTestScreenState();
}

class _TimeTestScreenState extends State<TimeTestScreen> {
  final ServerTimeService _serverTimeService = Get.find<ServerTimeService>();
  final SessionController _sessionController = Get.find<SessionController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Time Manipulation Test'),
        backgroundColor: Colors.teal,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Time Manipulation Resistance Test',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.teal,
              ),
            ),
            const SizedBox(height: 20),
            
            // Instructions
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.shade200),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Test Instructions:',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.orange,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    '1. Note the current countdown times below\n'
                    '2. Go to your device settings and change the system time\n'
                    '3. Return to this screen and refresh\n'
                    '4. Verify that countdown times are based on server time, not device time\n'
                    '5. Try accessing sessions - they should remain locked until server time allows',
                    style: TextStyle(fontSize: 14),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 20),
            
            // Time Information
            Obx(() => Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Time Information:',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    _buildTimeRow('Device Time:', DateTime.now().toString()),
                    _buildTimeRow('Server Time:', _serverTimeService.getServerTime().toString()),
                    _buildTimeRow('Time Offset:', '${_serverTimeService.timeOffset}ms'),
                    _buildTimeRow('Last Sync:', _serverTimeService.lastSyncTime?.toString() ?? 'Never'),
                    _buildTimeRow('Sync Status:', _serverTimeService.isTimeSyncRecent ? 'Recent' : 'Outdated'),
                  ],
                ),
              ),
            )),
            
            const SizedBox(height: 20),
            
            // Session Status
            Obx(() => Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Session Status (Server Time Based):',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    for (int i = 1; i <= 4; i++)
                      _buildSessionRow(i),
                  ],
                ),
              ),
            )),
            
            const SizedBox(height: 20),
            
            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () async {
                      await _serverTimeService.forceSyncWithServer();
                      await _sessionController.loadSessions();
                      setState(() {});
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.teal,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Refresh Data'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      Get.back();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Back'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimeRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontFamily: 'monospace'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSessionRow(int sessionNumber) {
    final isUnlocked = _sessionController.isSessionUnlockedByServerTime(sessionNumber);
    final countdown = _sessionController.getSessionCountdownByServerTime(sessionNumber);
    
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          SizedBox(
            width: 80,
            child: Text(
              'Session $sessionNumber:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: isUnlocked ? Colors.green.shade100 : Colors.red.shade100,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              isUnlocked ? 'UNLOCKED' : 'LOCKED',
              style: TextStyle(
                color: isUnlocked ? Colors.green.shade800 : Colors.red.shade800,
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
          ),
          const SizedBox(width: 12),
          if (!isUnlocked)
            Text(
              'Unlocks in: $countdown',
              style: const TextStyle(fontFamily: 'monospace'),
            ),
        ],
      ),
    );
  }
}
